#!/bin/bash

# 电影购票系统启动脚本
# 使用方法: ./start.sh [backend|admin|all]

echo "==================================="
echo "电影购票系统启动脚本"
echo "==================================="

# 检查参数
if [ $# -eq 0 ]; then
    echo "使用方法: ./start.sh [backend|admin|all]"
    echo "  backend - 启动后端服务"
    echo "  admin   - 启动管理后台"
    echo "  all     - 启动所有服务"
    exit 1
fi

# 启动后端服务
start_backend() {
    echo "启动后端服务..."
    cd weimai
    
    # 检查Java环境
    if ! command -v java &> /dev/null; then
        echo "错误: 未找到Java环境，请先安装JDK 8+"
        exit 1
    fi
    
    # 检查Maven环境
    if ! command -v mvn &> /dev/null; then
        echo "错误: 未找到Maven环境，请先安装Maven 3.6+"
        exit 1
    fi
    
    echo "正在编译后端项目..."
    mvn clean compile
    
    if [ $? -eq 0 ]; then
        echo "编译成功，启动Spring Boot应用..."
        mvn spring-boot:run &
        echo "后端服务启动中，请稍候..."
        echo "后端服务地址: http://localhost:8080/api"
    else
        echo "后端编译失败，请检查错误信息"
        exit 1
    fi
    
    cd ..
}

# 启动管理后台
start_admin() {
    echo "启动管理后台..."
    cd film_admin
    
    # 检查Node.js环境
    if ! command -v node &> /dev/null; then
        echo "错误: 未找到Node.js环境，请先安装Node.js 16+"
        exit 1
    fi
    
    # 检查npm环境
    if ! command -v npm &> /dev/null; then
        echo "错误: 未找到npm环境，请先安装npm"
        exit 1
    fi
    
    # 检查是否已安装依赖
    if [ ! -d "node_modules" ]; then
        echo "正在安装依赖..."
        npm install
        if [ $? -ne 0 ]; then
            echo "依赖安装失败，请检查网络连接"
            exit 1
        fi
    fi
    
    echo "启动Vue开发服务器..."
    npm run serve &
    echo "管理后台启动中，请稍候..."
    echo "管理后台地址: http://localhost:8081"
    
    cd ..
}

# 检查数据库连接
check_database() {
    echo "检查数据库连接..."
    
    # 这里可以添加数据库连接检查逻辑
    # mysql -h localhost -u root -p -e "SELECT 1" movie_ticket
    
    echo "请确保MySQL和Redis服务已启动"
    echo "数据库初始化脚本: sql/init.sql"
    echo "测试数据脚本: sql/data.sql"
}

# 显示启动信息
show_info() {
    echo ""
    echo "==================================="
    echo "服务启动完成！"
    echo "==================================="
    echo "后端API服务: http://localhost:8080/api"
    echo "管理后台: http://localhost:8081"
    echo "小程序: 使用微信开发者工具打开wea目录"
    echo ""
    echo "默认管理员账号:"
    echo "用户名: admin"
    echo "密码: 123456"
    echo ""
    echo "按 Ctrl+C 停止服务"
    echo "==================================="
}

# 主逻辑
case $1 in
    "backend")
        check_database
        start_backend
        show_info
        ;;
    "admin")
        start_admin
        show_info
        ;;
    "all")
        check_database
        start_backend
        sleep 5
        start_admin
        show_info
        ;;
    *)
        echo "无效参数: $1"
        echo "使用方法: ./start.sh [backend|admin|all]"
        exit 1
        ;;
esac

# 等待用户中断
wait
