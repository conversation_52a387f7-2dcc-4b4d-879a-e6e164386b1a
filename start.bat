@echo off
chcp 65001 >nul
setlocal enabledelayedexpansion

echo ===================================
echo 电影购票系统启动脚本 (Windows)
echo ===================================

if "%1"=="" (
    echo 使用方法: start.bat [backend^|admin^|all]
    echo   backend - 启动后端服务
    echo   admin   - 启动管理后台
    echo   all     - 启动所有服务
    pause
    exit /b 1
)

goto %1

:backend
echo 启动后端服务...
cd weimai

:: 检查Java环境
java -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Java环境，请先安装JDK 8+
    pause
    exit /b 1
)

:: 检查Maven环境
mvn -version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Maven环境，请先安装Maven 3.6+
    pause
    exit /b 1
)

echo 正在编译后端项目...
call mvn clean compile

if errorlevel 1 (
    echo 后端编译失败，请检查错误信息
    pause
    exit /b 1
)

echo 编译成功，启动Spring Boot应用...
start "后端服务" cmd /k "mvn spring-boot:run"
echo 后端服务启动中，请稍候...
echo 后端服务地址: http://localhost:8080/api

cd ..
goto show_info

:admin
echo 启动管理后台...
cd film_admin

:: 检查Node.js环境
node -v >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Node.js环境，请先安装Node.js 16+
    pause
    exit /b 1
)

:: 检查npm环境
npm -v >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到npm环境，请先安装npm
    pause
    exit /b 1
)

:: 检查是否已安装依赖
if not exist "node_modules" (
    echo 正在安装依赖...
    call npm install
    if errorlevel 1 (
        echo 依赖安装失败，请检查网络连接
        pause
        exit /b 1
    )
)

echo 启动Vue开发服务器...
start "管理后台" cmd /k "npm run serve"
echo 管理后台启动中，请稍候...
echo 管理后台地址: http://localhost:8081

cd ..
goto show_info

:all
echo 检查数据库连接...
echo 请确保MySQL和Redis服务已启动
echo 数据库初始化脚本: sql\init.sql
echo 测试数据脚本: sql\data.sql
echo.

call :backend
timeout /t 5 /nobreak >nul
call :admin
goto show_info

:show_info
echo.
echo ===================================
echo 服务启动完成！
echo ===================================
echo 后端API服务: http://localhost:8080/api
echo 管理后台: http://localhost:8081
echo 小程序: 使用微信开发者工具打开wea目录
echo.
echo 默认管理员账号:
echo 用户名: admin
echo 密码: 123456
echo.
echo 按任意键退出...
echo ===================================
pause >nul
exit /b 0

echo 无效参数: %1
echo 使用方法: start.bat [backend^|admin^|all]
pause
exit /b 1
