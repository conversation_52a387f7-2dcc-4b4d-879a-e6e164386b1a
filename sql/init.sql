-- 电影购票系统数据库初始化脚本
-- 创建数据库
CREATE DATABASE IF NOT EXISTS movie_ticket DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE movie_ticket;

-- 用户表
CREATE TABLE `user` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `nickname` varchar(50) DEFAULT NULL COMMENT '昵称',
  `avatar` varchar(255) DEFAULT NULL COMMENT '头像',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `gender` tinyint DEFAULT '0' COMMENT '性别：0-未知，1-男，2-女',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_openid` (`openid`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户表';

-- 管理员表
CREATE TABLE `admin` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '管理员ID',
  `username` varchar(50) NOT NULL COMMENT '用户名',
  `password` varchar(100) NOT NULL COMMENT '密码',
  `real_name` varchar(50) DEFAULT NULL COMMENT '真实姓名',
  `phone` varchar(20) DEFAULT NULL COMMENT '手机号',
  `email` varchar(100) DEFAULT NULL COMMENT '邮箱',
  `role` varchar(20) DEFAULT 'ADMIN' COMMENT '角色：SUPER_ADMIN-超级管理员，ADMIN-管理员',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-禁用，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_username` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='管理员表';

-- 电影表
CREATE TABLE `movie` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '电影ID',
  `name` varchar(100) NOT NULL COMMENT '电影名称',
  `english_name` varchar(100) DEFAULT NULL COMMENT '英文名称',
  `poster` varchar(255) DEFAULT NULL COMMENT '海报图片',
  `director` varchar(100) DEFAULT NULL COMMENT '导演',
  `actors` varchar(500) DEFAULT NULL COMMENT '主演',
  `type` varchar(100) DEFAULT NULL COMMENT '类型',
  `country` varchar(50) DEFAULT NULL COMMENT '国家/地区',
  `language` varchar(50) DEFAULT NULL COMMENT '语言',
  `duration` int DEFAULT NULL COMMENT '时长（分钟）',
  `release_date` date DEFAULT NULL COMMENT '上映日期',
  `rating` decimal(3,1) DEFAULT NULL COMMENT '评分',
  `description` text COMMENT '剧情简介',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-下架，1-上架',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='电影表';

-- 影院表
CREATE TABLE `cinema` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '影院ID',
  `name` varchar(100) NOT NULL COMMENT '影院名称',
  `address` varchar(255) DEFAULT NULL COMMENT '地址',
  `phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `longitude` decimal(10,7) DEFAULT NULL COMMENT '经度',
  `latitude` decimal(10,7) DEFAULT NULL COMMENT '纬度',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-关闭，1-营业',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='影院表';

-- 影厅表
CREATE TABLE `hall` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '影厅ID',
  `cinema_id` bigint NOT NULL COMMENT '影院ID',
  `name` varchar(50) NOT NULL COMMENT '影厅名称',
  `type` varchar(20) DEFAULT 'STANDARD' COMMENT '影厅类型：STANDARD-标准厅，IMAX-IMAX厅，VIP-VIP厅',
  `rows` int NOT NULL COMMENT '行数',
  `cols` int NOT NULL COMMENT '列数',
  `total_seats` int NOT NULL COMMENT '总座位数',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-维护，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  KEY `idx_cinema_id` (`cinema_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='影厅表';

-- 座位表
CREATE TABLE `seat` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '座位ID',
  `hall_id` bigint NOT NULL COMMENT '影厅ID',
  `row_num` int NOT NULL COMMENT '行号',
  `col_num` int NOT NULL COMMENT '列号',
  `seat_type` varchar(20) DEFAULT 'STANDARD' COMMENT '座位类型：STANDARD-标准座，VIP-VIP座，COUPLE-情侣座',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-损坏，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_hall_row_col` (`hall_id`, `row_num`, `col_num`),
  KEY `idx_hall_id` (`hall_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='座位表';

-- 排片表
CREATE TABLE `schedule` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '排片ID',
  `movie_id` bigint NOT NULL COMMENT '电影ID',
  `cinema_id` bigint NOT NULL COMMENT '影院ID',
  `hall_id` bigint NOT NULL COMMENT '影厅ID',
  `show_date` date NOT NULL COMMENT '放映日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `price` decimal(8,2) NOT NULL COMMENT '票价',
  `available_seats` int DEFAULT '0' COMMENT '可用座位数',
  `status` tinyint DEFAULT '1' COMMENT '状态：0-停售，1-正常',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  KEY `idx_movie_id` (`movie_id`),
  KEY `idx_cinema_id` (`cinema_id`),
  KEY `idx_hall_id` (`hall_id`),
  KEY `idx_show_date` (`show_date`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='排片表';

-- 订单表
CREATE TABLE `order_info` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '订单ID',
  `order_no` varchar(32) NOT NULL COMMENT '订单号',
  `user_id` bigint NOT NULL COMMENT '用户ID',
  `schedule_id` bigint NOT NULL COMMENT '排片ID',
  `movie_name` varchar(100) NOT NULL COMMENT '电影名称',
  `cinema_name` varchar(100) NOT NULL COMMENT '影院名称',
  `hall_name` varchar(50) NOT NULL COMMENT '影厅名称',
  `show_date` date NOT NULL COMMENT '放映日期',
  `start_time` time NOT NULL COMMENT '开始时间',
  `seat_info` varchar(500) NOT NULL COMMENT '座位信息',
  `seat_count` int NOT NULL COMMENT '座位数量',
  `unit_price` decimal(8,2) NOT NULL COMMENT '单价',
  `total_amount` decimal(8,2) NOT NULL COMMENT '总金额',
  `status` tinyint DEFAULT '0' COMMENT '订单状态：0-待支付，1-已支付，2-已取消，3-已退款',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `cancel_time` datetime DEFAULT NULL COMMENT '取消时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  `deleted` tinyint DEFAULT '0' COMMENT '删除标记：0-正常，1-删除',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_order_no` (`order_no`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_schedule_id` (`schedule_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单表';

-- 订单座位关联表
CREATE TABLE `order_seat` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT 'ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `seat_id` bigint NOT NULL COMMENT '座位ID',
  `row_num` int NOT NULL COMMENT '行号',
  `col_num` int NOT NULL COMMENT '列号',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  KEY `idx_order_id` (`order_id`),
  KEY `idx_seat_id` (`seat_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='订单座位关联表';

-- 支付记录表
CREATE TABLE `payment` (
  `id` bigint NOT NULL AUTO_INCREMENT COMMENT '支付ID',
  `order_id` bigint NOT NULL COMMENT '订单ID',
  `payment_no` varchar(32) NOT NULL COMMENT '支付流水号',
  `payment_type` varchar(20) DEFAULT 'WECHAT' COMMENT '支付方式：WECHAT-微信支付',
  `amount` decimal(8,2) NOT NULL COMMENT '支付金额',
  `status` tinyint DEFAULT '0' COMMENT '支付状态：0-待支付，1-支付成功，2-支付失败',
  `transaction_id` varchar(64) DEFAULT NULL COMMENT '第三方交易号',
  `pay_time` datetime DEFAULT NULL COMMENT '支付时间',
  `create_time` datetime DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_payment_no` (`payment_no`),
  KEY `idx_order_id` (`order_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';
