-- 电影购票系统测试数据
USE movie_ticket;

-- 插入管理员数据
INSERT INTO `admin` (`username`, `password`, `real_name`, `phone`, `email`, `role`) VALUES
('admin', '$2a$10$7JB720yubVSOfvVWdBYoOeymQxiXEJUYiCqOYyXgaVlFaYvYWScLq', '系统管理员', '13800138000', '<EMAIL>', 'SUPER_ADMIN'),
('manager', '$2a$10$7JB720yubVSOfvVWdBYoOeymQxiXEJUYiCqOYyXgaVlFaYvYWScLq', '影院经理', '13800138001', '<EMAIL>', 'ADMIN');

-- 插入影院数据
INSERT INTO `cinema` (`name`, `address`, `phone`, `longitude`, `latitude`) VALUES
('万达影城(王府井店)', '北京市东城区王府井大街255号', '010-12345678', 116.417592, 39.909736),
('CGV影城(朝阳大悦城店)', '北京市朝阳区朝阳北路101号', '010-87654321', 116.485319, 39.928353),
('博纳国际影城(中关村店)', '北京市海淀区中关村大街1号', '010-11223344', 116.310003, 39.983497);

-- 插入影厅数据
INSERT INTO `hall` (`cinema_id`, `name`, `type`, `rows`, `cols`, `total_seats`) VALUES
(1, '1号厅', 'STANDARD', 10, 12, 120),
(1, '2号厅', 'VIP', 8, 10, 80),
(1, '3号厅', 'IMAX', 12, 15, 180),
(2, '1号厅', 'STANDARD', 9, 11, 99),
(2, '2号厅', 'VIP', 7, 9, 63),
(3, '1号厅', 'STANDARD', 11, 13, 143),
(3, '2号厅', 'IMAX', 13, 16, 208);

-- 插入电影数据
INSERT INTO `movie` (`name`, `english_name`, `poster`, `director`, `actors`, `type`, `country`, `language`, `duration`, `release_date`, `rating`, `description`) VALUES
('流浪地球2', 'The Wandering Earth II', '/images/movies/liulangdiqiu2.jpg', '郭帆', '吴京,刘德华,李雪健,沙溢,宁理,王智,朱颜曼滋', '科幻/冒险/灾难', '中国大陆', '汉语普通话', 173, '2023-01-22', 8.3, '太阳即将毁灭，人类在地球表面建造出巨大的推进器，寻找新的家园。然而宇宙之路危机四伏，为了拯救地球，流浪地球时代的年轻人再次挺身而出，展开争分夺秒的生死之战。'),
('满江红', 'Full River Red', '/images/movies/manjianhong.jpg', '张艺谋', '沈腾,易烊千玺,张译,雷佳音,岳云鹏,王佳怡', '剧情/喜剧/悬疑', '中国大陆', '汉语普通话', 159, '2023-01-22', 8.0, '南宋绍兴年间，岳飞死后四年，秦桧率兵与金国会谈。会谈前夜，金国使者死在宰相驻地，密信也不翼而飞。小兵张大与亲兵营副统领孙均受命调查此事。'),
('深海', 'Deep Sea', '/images/movies/shenhai.jpg', '田晓鹏', '苏鑫,王亭文,滕奎兴,李兰陵', '动画/奇幻/冒险', '中国大陆', '汉语普通话', 112, '2023-01-22', 7.8, '一位少女在神秘的深海世界中追寻探索，邂逅一段独特的生命旅程。'),
('阿凡达：水之道', 'Avatar: The Way of Water', '/images/movies/avatar2.jpg', '詹姆斯·卡梅隆', '萨姆·沃辛顿,佐伊·索尔达娜,西格妮·韦弗,史蒂芬·朗', '科幻/动作/冒险', '美国', '英语', 192, '2022-12-16', 8.1, '杰克·萨利已经成为纳美族的一员，与妻子娜塔莉共同育有一对可爱的儿女，日子过得平淡而充实。然而某天，有个部族的兄弟在海岸附近巡逻时遭到利器割喉身亡。'),
('黑豹2：瓦坎达万岁', 'Black Panther: Wakanda Forever', '/images/movies/blackpanther2.jpg', '瑞恩·库格勒', '安吉拉·贝塞特,利蒂希娅·赖特,丹娜·奎里拉,温斯顿·杜克', '动作/科幻/冒险', '美国', '英语', 161, '2022-11-11', 7.5, '瓦坎达国王特查拉去世一年后，苏睿、奥克耶、拉玛达女王、姆巴库努力保护瓦坎达不受世界列强的侵犯。');

-- 插入座位数据（为1号影院1号厅生成座位）
INSERT INTO `seat` (`hall_id`, `row_num`, `col_num`, `seat_type`) VALUES
-- 第1-3行：标准座
(1, 1, 1, 'STANDARD'), (1, 1, 2, 'STANDARD'), (1, 1, 3, 'STANDARD'), (1, 1, 4, 'STANDARD'), (1, 1, 5, 'STANDARD'), (1, 1, 6, 'STANDARD'), (1, 1, 7, 'STANDARD'), (1, 1, 8, 'STANDARD'), (1, 1, 9, 'STANDARD'), (1, 1, 10, 'STANDARD'), (1, 1, 11, 'STANDARD'), (1, 1, 12, 'STANDARD'),
(1, 2, 1, 'STANDARD'), (1, 2, 2, 'STANDARD'), (1, 2, 3, 'STANDARD'), (1, 2, 4, 'STANDARD'), (1, 2, 5, 'STANDARD'), (1, 2, 6, 'STANDARD'), (1, 2, 7, 'STANDARD'), (1, 2, 8, 'STANDARD'), (1, 2, 9, 'STANDARD'), (1, 2, 10, 'STANDARD'), (1, 2, 11, 'STANDARD'), (1, 2, 12, 'STANDARD'),
(1, 3, 1, 'STANDARD'), (1, 3, 2, 'STANDARD'), (1, 3, 3, 'STANDARD'), (1, 3, 4, 'STANDARD'), (1, 3, 5, 'STANDARD'), (1, 3, 6, 'STANDARD'), (1, 3, 7, 'STANDARD'), (1, 3, 8, 'STANDARD'), (1, 3, 9, 'STANDARD'), (1, 3, 10, 'STANDARD'), (1, 3, 11, 'STANDARD'), (1, 3, 12, 'STANDARD');

-- 插入排片数据
INSERT INTO `schedule` (`movie_id`, `cinema_id`, `hall_id`, `show_date`, `start_time`, `end_time`, `price`, `available_seats`) VALUES
-- 今天的排片
(1, 1, 1, CURDATE(), '09:00:00', '12:00:00', 45.00, 120),
(1, 1, 1, CURDATE(), '14:30:00', '17:30:00', 45.00, 120),
(1, 1, 1, CURDATE(), '20:00:00', '23:00:00', 55.00, 120),
(2, 1, 2, CURDATE(), '10:00:00', '12:40:00', 65.00, 80),
(2, 1, 2, CURDATE(), '15:00:00', '17:40:00', 65.00, 80),
(2, 1, 2, CURDATE(), '21:00:00', '23:40:00', 75.00, 80),
(3, 1, 3, CURDATE(), '11:00:00', '13:00:00', 85.00, 180),
(3, 1, 3, CURDATE(), '16:00:00', '18:00:00', 85.00, 180),
(4, 2, 1, CURDATE(), '09:30:00', '12:45:00', 50.00, 99),
(4, 2, 1, CURDATE(), '19:30:00', '22:45:00', 60.00, 99),
(5, 2, 2, CURDATE(), '13:00:00', '15:45:00', 70.00, 63),
-- 明天的排片
(1, 1, 1, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '09:00:00', '12:00:00', 45.00, 120),
(2, 1, 2, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '14:00:00', '16:40:00', 65.00, 80),
(3, 1, 3, DATE_ADD(CURDATE(), INTERVAL 1 DAY), '20:00:00', '22:00:00', 85.00, 180);
