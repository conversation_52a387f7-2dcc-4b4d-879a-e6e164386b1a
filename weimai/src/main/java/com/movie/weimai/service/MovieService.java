package com.movie.weimai.service;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.movie.weimai.entity.Movie;

/**
 * 电影服务接口
 * 
 * <AUTHOR> Team
 * @since 2023-08-13
 */
public interface MovieService extends IService<Movie> {

    /**
     * 分页查询电影列表
     * 
     * @param current 当前页
     * @param size 每页大小
     * @param name 电影名称（可选）
     * @param status 状态（可选）
     * @return 分页结果
     */
    Page<Movie> getMoviePage(Long current, Long size, String name, Integer status);

    /**
     * 获取正在上映的电影列表
     * 
     * @return 电影列表
     */
    Page<Movie> getNowShowingMovies(Long current, Long size);

    /**
     * 获取即将上映的电影列表
     * 
     * @return 电影列表
     */
    Page<Movie> getComingSoonMovies(Long current, Long size);
}
