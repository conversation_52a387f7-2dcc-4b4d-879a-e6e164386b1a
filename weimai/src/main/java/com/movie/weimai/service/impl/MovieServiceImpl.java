package com.movie.weimai.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.movie.weimai.entity.Movie;
import com.movie.weimai.mapper.MovieMapper;
import com.movie.weimai.service.MovieService;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import java.time.LocalDate;

/**
 * 电影服务实现类
 * 
 * <AUTHOR> Team
 * @since 2023-08-13
 */
@Service
public class MovieServiceImpl extends ServiceImpl<MovieMapper, Movie> implements MovieService {

    @Override
    public Page<Movie> getMoviePage(Long current, Long size, String name, Integer status) {
        Page<Movie> page = new Page<>(current, size);
        LambdaQueryWrapper<Movie> queryWrapper = new LambdaQueryWrapper<>();
        
        // 根据电影名称模糊查询
        if (StringUtils.hasText(name)) {
            queryWrapper.like(Movie::getName, name);
        }
        
        // 根据状态查询
        if (status != null) {
            queryWrapper.eq(Movie::getStatus, status);
        }
        
        // 按创建时间倒序
        queryWrapper.orderByDesc(Movie::getCreateTime);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public Page<Movie> getNowShowingMovies(Long current, Long size) {
        Page<Movie> page = new Page<>(current, size);
        LambdaQueryWrapper<Movie> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态为上架
        queryWrapper.eq(Movie::getStatus, 1);
        // 上映日期小于等于今天
        queryWrapper.le(Movie::getReleaseDate, LocalDate.now());
        // 按评分倒序
        queryWrapper.orderByDesc(Movie::getRating);
        
        return this.page(page, queryWrapper);
    }

    @Override
    public Page<Movie> getComingSoonMovies(Long current, Long size) {
        Page<Movie> page = new Page<>(current, size);
        LambdaQueryWrapper<Movie> queryWrapper = new LambdaQueryWrapper<>();
        
        // 状态为上架
        queryWrapper.eq(Movie::getStatus, 1);
        // 上映日期大于今天
        queryWrapper.gt(Movie::getReleaseDate, LocalDate.now());
        // 按上映日期正序
        queryWrapper.orderByAsc(Movie::getReleaseDate);
        
        return this.page(page, queryWrapper);
    }
}
