package com.movie.weimai.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.movie.weimai.common.Result;
import com.movie.weimai.entity.Movie;
import com.movie.weimai.service.MovieService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * 电影控制器
 * 
 * <AUTHOR> Team
 * @since 2023-08-13
 */
@RestController
@RequestMapping("/movie")
@RequiredArgsConstructor
public class MovieController {

    private final MovieService movieService;

    /**
     * 分页查询电影列表（管理端）
     */
    @GetMapping("/page")
    public Result<Page<Movie>> getMoviePage(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size,
            @RequestParam(required = false) String name,
            @RequestParam(required = false) Integer status) {
        
        Page<Movie> page = movieService.getMoviePage(current, size, name, status);
        return Result.success(page);
    }

    /**
     * 获取正在上映的电影列表（小程序端）
     */
    @GetMapping("/now-showing")
    public Result<Page<Movie>> getNowShowingMovies(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        Page<Movie> page = movieService.getNowShowingMovies(current, size);
        return Result.success(page);
    }

    /**
     * 获取即将上映的电影列表（小程序端）
     */
    @GetMapping("/coming-soon")
    public Result<Page<Movie>> getComingSoonMovies(
            @RequestParam(defaultValue = "1") Long current,
            @RequestParam(defaultValue = "10") Long size) {
        
        Page<Movie> page = movieService.getComingSoonMovies(current, size);
        return Result.success(page);
    }

    /**
     * 根据ID查询电影详情
     */
    @GetMapping("/{id}")
    public Result<Movie> getMovieById(@PathVariable Long id) {
        Movie movie = movieService.getById(id);
        if (movie == null) {
            return Result.notFound("电影不存在");
        }
        return Result.success(movie);
    }

    /**
     * 新增电影（管理端）
     */
    @PostMapping
    public Result<Void> addMovie(@RequestBody Movie movie) {
        boolean success = movieService.save(movie);
        return success ? Result.success("添加成功") : Result.error("添加失败");
    }

    /**
     * 更新电影（管理端）
     */
    @PutMapping("/{id}")
    public Result<Void> updateMovie(@PathVariable Long id, @RequestBody Movie movie) {
        movie.setId(id);
        boolean success = movieService.updateById(movie);
        return success ? Result.success("更新成功") : Result.error("更新失败");
    }

    /**
     * 删除电影（管理端）
     */
    @DeleteMapping("/{id}")
    public Result<Void> deleteMovie(@PathVariable Long id) {
        boolean success = movieService.removeById(id);
        return success ? Result.success("删除成功") : Result.error("删除失败");
    }
}
