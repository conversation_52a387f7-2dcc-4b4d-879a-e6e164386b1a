package com.movie.weimai.entity;

import com.baomidou.mybatisplus.annotation.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * 电影实体类
 * 
 * <AUTHOR> Team
 * @since 2023-08-13
 */
@Data
@EqualsAndHashCode(callSuper = false)
@TableName("movie")
public class Movie {

    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 电影名称
     */
    private String name;

    /**
     * 英文名称
     */
    private String englishName;

    /**
     * 海报图片
     */
    private String poster;

    /**
     * 导演
     */
    private String director;

    /**
     * 主演
     */
    private String actors;

    /**
     * 类型
     */
    private String type;

    /**
     * 国家/地区
     */
    private String country;

    /**
     * 语言
     */
    private String language;

    /**
     * 时长（分钟）
     */
    private Integer duration;

    /**
     * 上映日期
     */
    private LocalDate releaseDate;

    /**
     * 评分
     */
    private BigDecimal rating;

    /**
     * 剧情简介
     */
    private String description;

    /**
     * 状态：0-下架，1-上架
     */
    private Integer status;

    /**
     * 创建时间
     */
    @TableField(fill = FieldFill.INSERT)
    private LocalDateTime createTime;

    /**
     * 更新时间
     */
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private LocalDateTime updateTime;

    /**
     * 删除标记：0-正常，1-删除
     */
    @TableLogic
    private Integer deleted;
}
