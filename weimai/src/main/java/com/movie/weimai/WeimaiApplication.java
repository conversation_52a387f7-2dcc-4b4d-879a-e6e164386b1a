package com.movie.weimai;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;

/**
 * 电影购票系统启动类
 * 
 * <AUTHOR> Team
 * @since 2023-08-13
 */
@SpringBootApplication
@MapperScan("com.movie.weimai.mapper")
public class WeimaiApplication {

    public static void main(String[] args) {
        SpringApplication.run(WeimaiApplication.class, args);
        System.out.println("=================================");
        System.out.println("电影购票系统启动成功！");
        System.out.println("接口文档地址：http://localhost:8080/api/doc.html");
        System.out.println("=================================");
    }
}
