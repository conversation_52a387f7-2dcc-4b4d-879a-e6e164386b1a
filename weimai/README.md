# 电影购票系统后端 (weimai)

基于Spring Boot 2.7的电影购票系统后端API服务。

## 技术栈

- **框架**: Spring Boot 2.7.14
- **数据库**: MySQL 8.0+
- **ORM**: MyBatis Plus 3.5.3
- **缓存**: Redis
- **连接池**: Druid
- **认证**: JWT
- **工具类**: Hutool

## 项目结构

```
weimai/
├── src/
│   └── main/
│       ├── java/
│       │   └── com/movie/weimai/
│       │       ├── WeimaiApplication.java    # 启动类
│       │       ├── common/                   # 通用类
│       │       │   └── Result.java          # 统一响应结果
│       │       ├── config/                   # 配置类
│       │       ├── controller/               # 控制器
│       │       │   └── MovieController.java
│       │       ├── entity/                   # 实体类
│       │       │   ├── User.java
│       │       │   └── Movie.java
│       │       ├── mapper/                   # Mapper接口
│       │       │   └── MovieMapper.java
│       │       ├── service/                  # 服务接口
│       │       │   └── MovieService.java
│       │       └── service/impl/             # 服务实现
│       │           └── MovieServiceImpl.java
│       └── resources/
│           ├── application.yml               # 配置文件
│           └── mapper/                       # MyBatis XML
├── pom.xml                                   # Maven配置
└── README.md
```

## 快速开始

### 1. 环境要求

- JDK 8+
- Maven 3.6+
- MySQL 8.0+
- Redis 6.0+

### 2. 数据库初始化

```bash
# 1. 创建数据库
mysql -u root -p
source sql/init.sql

# 2. 导入测试数据
source sql/data.sql
```

### 3. 修改配置

编辑 `src/main/resources/application.yml`：

```yaml
spring:
  datasource:
    url: ****************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
    password: your_redis_password
```

### 4. 启动项目

```bash
# 使用Maven启动
mvn spring-boot:run

# 或者使用IDE运行WeimaiApplication.java
```

启动成功后访问：http://localhost:8080/api

## API文档

### 电影相关接口

#### 1. 获取正在上映电影列表
```
GET /api/movie/now-showing
参数：
- current: 当前页（默认1）
- size: 每页大小（默认10）
```

#### 2. 获取即将上映电影列表
```
GET /api/movie/coming-soon
参数：
- current: 当前页（默认1）
- size: 每页大小（默认10）
```

#### 3. 获取电影详情
```
GET /api/movie/{id}
```

#### 4. 分页查询电影（管理端）
```
GET /api/movie/page
参数：
- current: 当前页
- size: 每页大小
- name: 电影名称（可选）
- status: 状态（可选）
```

#### 5. 新增电影（管理端）
```
POST /api/movie
Content-Type: application/json

{
  "name": "电影名称",
  "englishName": "English Name",
  "director": "导演",
  "actors": "主演",
  "type": "类型",
  "duration": 120,
  "releaseDate": "2023-08-13",
  "rating": 8.5,
  "description": "剧情简介"
}
```

## 开发说明

### 1. 代码规范

- 使用Lombok简化代码
- 统一使用Result类封装响应结果
- 遵循RESTful API设计规范
- 使用MyBatis Plus简化数据库操作

### 2. 异常处理

- 全局异常处理器统一处理异常
- 业务异常使用自定义异常类
- 返回统一的错误码和错误信息

### 3. 安全认证

- 使用JWT进行用户认证
- 接口权限控制
- 密码加密存储

## 部署说明

### 1. 打包

```bash
mvn clean package -Dmaven.test.skip=true
```

### 2. 运行

```bash
java -jar target/weimai-1.0.0.jar
```

### 3. Docker部署

```dockerfile
FROM openjdk:8-jre-alpine
COPY target/weimai-1.0.0.jar app.jar
EXPOSE 8080
ENTRYPOINT ["java", "-jar", "/app.jar"]
```

## 注意事项

1. 首次启动前请确保数据库和Redis服务正常运行
2. 修改配置文件中的数据库连接信息
3. 生产环境请修改JWT密钥和Redis密码
4. 建议使用Nginx进行反向代理
