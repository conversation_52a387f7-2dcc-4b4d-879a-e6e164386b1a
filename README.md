# 电影购票小程序系统

## 项目简介
这是一个基于Spring Boot + Vue + 微信小程序的电影购票系统，包含用户端小程序、管理后台和后端API。

## 项目结构
```
movie-ticket-system/
├── weimai/                 # Spring Boot后端项目
│   ├── src/
│   │   └── main/
│   │       ├── java/
│   │       └── resources/
│   ├── pom.xml
│   └── README.md
├── film_admin/             # Vue管理后台
│   ├── src/
│   ├── public/
│   ├── package.json
│   └── README.md
├── wea/                    # 微信小程序前端
│   ├── pages/
│   ├── components/
│   ├── app.js
│   ├── app.json
│   └── README.md
├── sql/                    # 数据库文件
│   ├── init.sql
│   └── data.sql
├── images/                 # 演示图片
└── README.md
```

## 功能模块

### 用户端小程序 (wea)
- 电影列表展示
- 电影详情查看
- 座位选择
- 在线支付
- 订单管理
- 用户中心

### 管理后台 (film_admin)
- 电影管理
- 影院管理
- 排片管理
- 订单管理
- 用户管理
- 数据统计

### 后端API (weimai)
- 用户认证与授权
- 电影信息管理
- 订票业务逻辑
- 支付接口集成
- 数据统计分析

## 技术栈
- **后端**: Spring Boot 2.7+, MyBatis Plus, MySQL, Redis
- **前端管理**: Vue 3, Element Plus, Axios
- **小程序**: 微信小程序原生开发
- **数据库**: MySQL 8.0+

## 快速开始

### 环境要求
- **Java**: JDK 8+
- **Node.js**: 16+
- **MySQL**: 8.0+
- **Redis**: 6.0+
- **Maven**: 3.6+
- **微信开发者工具**: 最新版

### 一键启动

#### Linux/Mac系统
```bash
# 给脚本执行权限
chmod +x start.sh

# 启动所有服务
./start.sh all

# 或单独启动
./start.sh backend  # 仅启动后端
./start.sh admin    # 仅启动管理后台
```

#### Windows系统
```cmd
# 启动所有服务
start.bat all

# 或单独启动
start.bat backend  # 仅启动后端
start.bat admin    # 仅启动管理后台
```

### 手动启动

#### 1. 数据库初始化
```sql
-- 创建数据库并导入数据
mysql -u root -p
source sql/init.sql
source sql/data.sql
```

#### 2. 启动后端服务
```bash
cd weimai
mvn spring-boot:run
```
访问地址：http://localhost:8080/api

#### 3. 启动管理后台
```bash
cd film_admin
npm install
npm run serve
```
访问地址：http://localhost:8081

#### 4. 启动小程序
1. 打开微信开发者工具
2. 导入项目，选择 `wea` 目录
3. 填入小程序AppID
4. 点击预览

### 默认账号
- **管理后台**
  - 用户名：admin
  - 密码：123456

## 项目演示

### 管理后台功能
- ✅ 电影信息管理（增删改查）
- ✅ 影院信息管理
- ✅ 排片计划管理
- ✅ 订单统计查看
- ✅ 用户数据管理

### 小程序功能
- ✅ 电影列表浏览
- ✅ 电影详情查看
- ✅ 影院位置查找
- ✅ 在线选座购票
- ✅ 订单管理
- ✅ 用户中心

## 开发指南

### 后端开发
详细说明请查看：[weimai/README.md](weimai/README.md)

### 前端开发
详细说明请查看：[film_admin/README.md](film_admin/README.md)

### 小程序开发
详细说明请查看：[wea/README.md](wea/README.md)
