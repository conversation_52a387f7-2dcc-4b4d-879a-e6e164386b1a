// app.js
App({
  globalData: {
    userInfo: null,
    token: null,
    baseUrl: 'http://localhost:8080/api'
  },

  onLaunch() {
    // 展示本地存储能力
    const logs = wx.getStorageSync('logs') || []
    logs.unshift(Date.now())
    wx.setStorageSync('logs', logs)

    // 获取用户信息
    this.getUserInfo()
  },

  // 获取用户信息
  getUserInfo() {
    const token = wx.getStorageSync('token')
    if (token) {
      this.globalData.token = token
      // 验证token有效性
      this.checkToken()
    }
  },

  // 验证token
  checkToken() {
    wx.request({
      url: this.globalData.baseUrl + '/user/info',
      header: {
        'Authorization': 'Bearer ' + this.globalData.token
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.globalData.userInfo = res.data.data
        } else {
          // token无效，清除本地存储
          wx.removeStorageSync('token')
          this.globalData.token = null
        }
      }
    })
  },

  // 微信登录
  login() {
    return new Promise((resolve, reject) => {
      wx.login({
        success: (res) => {
          if (res.code) {
            // 发送 res.code 到后台换取 openId, sessionKey, unionId
            wx.request({
              url: this.globalData.baseUrl + '/auth/login',
              method: 'POST',
              data: {
                code: res.code
              },
              success: (response) => {
                if (response.data.code === 200) {
                  const { token, userInfo } = response.data.data
                  this.globalData.token = token
                  this.globalData.userInfo = userInfo
                  wx.setStorageSync('token', token)
                  resolve(response.data.data)
                } else {
                  reject(response.data.message)
                }
              },
              fail: (error) => {
                reject(error)
              }
            })
          } else {
            reject('登录失败！' + res.errMsg)
          }
        }
      })
    })
  },

  // 网络请求封装
  request(options) {
    return new Promise((resolve, reject) => {
      wx.request({
        url: this.globalData.baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data || {},
        header: {
          'Content-Type': 'application/json',
          'Authorization': this.globalData.token ? 'Bearer ' + this.globalData.token : '',
          ...options.header
        },
        success: (res) => {
          if (res.data.code === 200) {
            resolve(res.data)
          } else if (res.data.code === 401) {
            // 未授权，跳转登录
            wx.removeStorageSync('token')
            this.globalData.token = null
            this.globalData.userInfo = null
            wx.showToast({
              title: '请先登录',
              icon: 'none'
            })
            reject(res.data)
          } else {
            wx.showToast({
              title: res.data.message || '请求失败',
              icon: 'none'
            })
            reject(res.data)
          }
        },
        fail: (error) => {
          wx.showToast({
            title: '网络错误',
            icon: 'none'
          })
          reject(error)
        }
      })
    })
  }
})
