# 电影购票小程序 (wea)

基于微信小程序原生开发的电影购票客户端。

## 功能特性

### 1. 首页
- 电影轮播图展示
- 正在热映电影列表
- 即将上映电影预告
- 快捷导航菜单

### 2. 电影模块
- 电影详情查看
- 电影评分和评论
- 电影预告片播放
- 相关电影推荐

### 3. 影院模块
- 附近影院查找
- 影院详情和地址
- 影院联系方式
- 地图导航功能

### 4. 购票模块
- 场次时间选择
- 座位选择界面
- 票价计算
- 在线支付

### 5. 订单模块
- 订单列表查看
- 订单详情
- 订单状态跟踪
- 退票申请

### 6. 用户模块
- 微信授权登录
- 个人信息管理
- 观影历史
- 收藏电影

## 项目结构

```
wea/
├── pages/                        # 页面目录
│   ├── index/                    # 首页
│   │   ├── index.js
│   │   ├── index.wxml
│   │   ├── index.wxss
│   │   └── index.json
│   ├── movie/                    # 电影相关
│   │   └── detail/               # 电影详情
│   ├── cinema/                   # 影院相关
│   │   └── list/                 # 影院列表
│   ├── schedule/                 # 排片相关
│   │   └── list/                 # 场次列表
│   ├── seat/                     # 座位选择
│   │   └── select/
│   ├── order/                    # 订单相关
│   │   ├── list/                 # 订单列表
│   │   └── detail/               # 订单详情
│   └── user/                     # 用户中心
│       └── index/
├── components/                   # 自定义组件
├── utils/                        # 工具函数
├── images/                       # 图片资源
├── app.js                        # 小程序逻辑
├── app.json                      # 小程序配置
├── app.wxss                      # 全局样式
├── sitemap.json                  # 站点地图
└── README.md
```

## 快速开始

### 1. 环境要求

- 微信开发者工具
- 微信小程序账号
- 后端API服务

### 2. 配置小程序

1. 在微信公众平台注册小程序账号
2. 获取AppID和AppSecret
3. 配置服务器域名

### 3. 修改配置

编辑 `app.js` 中的配置：

```javascript
App({
  globalData: {
    baseUrl: 'https://your-api-domain.com/api', // 修改为实际API地址
    // 其他配置...
  }
})
```

### 4. 导入项目

1. 打开微信开发者工具
2. 选择"导入项目"
3. 选择项目目录
4. 填入AppID

### 5. 预览和发布

1. 点击"预览"生成二维码
2. 使用微信扫码预览
3. 测试完成后提交审核
4. 审核通过后发布上线

## 页面说明

### 1. 首页 (pages/index/index)
- 展示电影轮播图
- 显示正在热映和即将上映电影
- 提供快捷导航入口

### 2. 电影详情 (pages/movie/detail)
- 显示电影基本信息
- 展示电影海报和剧照
- 提供购票入口

### 3. 影院列表 (pages/cinema/list)
- 显示附近影院
- 支持地图查看
- 提供导航功能

### 4. 场次列表 (pages/schedule/list)
- 显示电影排片信息
- 按日期和时间筛选
- 显示票价和余票

### 5. 座位选择 (pages/seat/select)
- 可视化座位布局
- 支持多座位选择
- 实时显示价格

### 6. 订单管理 (pages/order/list)
- 显示用户订单历史
- 支持订单状态查询
- 提供退票功能

## 开发规范

### 1. 命名规范

- 页面文件使用小写字母和连字符
- 组件使用PascalCase命名
- 变量使用camelCase命名

### 2. 样式规范

- 使用rpx作为尺寸单位
- 遵循微信小程序设计规范
- 适配不同屏幕尺寸

### 3. 数据请求

```javascript
// 使用app.request方法进行API调用
const app = getApp()

app.request({
  url: '/movie/list',
  method: 'GET',
  data: {
    page: 1,
    size: 10
  }
}).then(res => {
  // 处理成功响应
}).catch(err => {
  // 处理错误
})
```

### 4. 用户授权

```javascript
// 获取用户信息
wx.getUserProfile({
  desc: '用于完善用户资料',
  success: (res) => {
    // 处理用户信息
  }
})

// 微信登录
app.login().then(res => {
  // 登录成功
}).catch(err => {
  // 登录失败
})
```

## 注意事项

### 1. 域名配置

在小程序后台配置request合法域名：
- https://your-api-domain.com

### 2. 权限申请

根据功能需要申请相应权限：
- 用户信息权限
- 地理位置权限
- 微信支付权限

### 3. 性能优化

- 图片使用webp格式
- 合理使用分包加载
- 避免频繁的setData操作

### 4. 兼容性

- 设置最低基础库版本
- 做好低版本兼容处理
- 测试不同机型表现

## 发布流程

1. 代码开发完成
2. 本地测试通过
3. 上传代码到微信后台
4. 提交审核
5. 审核通过后发布

## 常见问题

### 1. 网络请求失败
- 检查域名是否已配置
- 确认API服务是否正常
- 查看控制台错误信息

### 2. 授权失败
- 检查AppID是否正确
- 确认权限配置是否完整
- 查看用户是否拒绝授权

### 3. 支付问题
- 确认商户号配置
- 检查支付参数
- 查看支付回调处理
