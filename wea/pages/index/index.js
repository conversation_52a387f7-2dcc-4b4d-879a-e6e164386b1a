// pages/index/index.js
const app = getApp()

Page({
  data: {
    banners: [
      {
        id: 1,
        image: '/images/banner1.jpg'
      },
      {
        id: 2,
        image: '/images/banner2.jpg'
      },
      {
        id: 3,
        image: '/images/banner3.jpg'
      }
    ],
    nowShowingMovies: [],
    comingSoonMovies: []
  },

  onLoad() {
    this.loadNowShowingMovies()
    this.loadComingSoonMovies()
  },

  onShow() {
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setData({
        selected: 0
      })
    }
  },

  // 加载正在热映电影
  loadNowShowingMovies() {
    app.request({
      url: '/movie/now-showing',
      data: {
        current: 1,
        size: 10
      }
    }).then(res => {
      this.setData({
        nowShowingMovies: res.data.records
      })
    }).catch(err => {
      console.error('加载正在热映电影失败:', err)
    })
  },

  // 加载即将上映电影
  loadComingSoonMovies() {
    app.request({
      url: '/movie/coming-soon',
      data: {
        current: 1,
        size: 10
      }
    }).then(res => {
      this.setData({
        comingSoonMovies: res.data.records
      })
    }).catch(err => {
      console.error('加载即将上映电影失败:', err)
    })
  },

  // 跳转到电影详情
  goToMovieDetail(e) {
    const movieId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/movie/detail?id=${movieId}`
    })
  },

  // 跳转到影院列表
  goToCinema() {
    wx.switchTab({
      url: '/pages/cinema/list'
    })
  },

  // 跳转到订单列表
  goToOrder() {
    wx.switchTab({
      url: '/pages/order/list'
    })
  },

  // 跳转到个人中心
  goToUser() {
    wx.switchTab({
      url: '/pages/user/index'
    })
  },

  // 查看更多正在热映
  viewMoreNowShowing() {
    // 可以跳转到专门的电影列表页面
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  },

  // 查看更多即将上映
  viewMoreComingSoon() {
    // 可以跳转到专门的电影列表页面
    wx.showToast({
      title: '功能开发中',
      icon: 'none'
    })
  }
})
