<!--pages/index/index.wxml-->
<view class="container">
  <!-- 轮播图 -->
  <swiper class="banner" indicator-dots="true" autoplay="true" interval="3000" duration="500">
    <swiper-item wx:for="{{banners}}" wx:key="id">
      <image src="{{item.image}}" mode="aspectFill" class="banner-image" />
    </swiper-item>
  </swiper>

  <!-- 导航菜单 -->
  <view class="nav-menu">
    <view class="nav-item" bindtap="goToCinema">
      <image src="/images/cinema-icon.png" class="nav-icon" />
      <text class="nav-text">选影院</text>
    </view>
    <view class="nav-item" bindtap="goToOrder">
      <image src="/images/order-icon.png" class="nav-icon" />
      <text class="nav-text">我的订单</text>
    </view>
    <view class="nav-item" bindtap="goToUser">
      <image src="/images/user-icon.png" class="nav-icon" />
      <text class="nav-text">个人中心</text>
    </view>
  </view>

  <!-- 正在热映 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">正在热映</text>
      <text class="section-more" bindtap="viewMoreNowShowing">更多 ></text>
    </view>
    <scroll-view class="movie-scroll" scroll-x="true">
      <view class="movie-list">
        <view class="movie-item" wx:for="{{nowShowingMovies}}" wx:key="id" bindtap="goToMovieDetail" data-id="{{item.id}}">
          <image src="{{item.poster}}" class="movie-poster" mode="aspectFill" />
          <view class="movie-info">
            <text class="movie-name">{{item.name}}</text>
            <view class="movie-rating">
              <text class="rating-score">{{item.rating}}</text>
              <text class="rating-text">分</text>
            </view>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 即将上映 -->
  <view class="section">
    <view class="section-header">
      <text class="section-title">即将上映</text>
      <text class="section-more" bindtap="viewMoreComingSoon">更多 ></text>
    </view>
    <scroll-view class="movie-scroll" scroll-x="true">
      <view class="movie-list">
        <view class="movie-item" wx:for="{{comingSoonMovies}}" wx:key="id" bindtap="goToMovieDetail" data-id="{{item.id}}">
          <image src="{{item.poster}}" class="movie-poster" mode="aspectFill" />
          <view class="movie-info">
            <text class="movie-name">{{item.name}}</text>
            <text class="movie-date">{{item.releaseDate}}</text>
          </view>
        </view>
      </view>
    </scroll-view>
  </view>
</view>
