/* pages/index/index.wxss */
.container {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 轮播图样式 */
.banner {
  width: 100%;
  height: 400rpx;
}

.banner-image {
  width: 100%;
  height: 100%;
}

/* 导航菜单样式 */
.nav-menu {
  display: flex;
  background-color: #fff;
  padding: 30rpx 0;
  margin-bottom: 20rpx;
}

.nav-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.nav-icon {
  width: 60rpx;
  height: 60rpx;
  margin-bottom: 10rpx;
}

.nav-text {
  font-size: 28rpx;
  color: #333;
}

/* 分区样式 */
.section {
  background-color: #fff;
  margin-bottom: 20rpx;
  padding: 30rpx 0;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 30rpx;
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.section-more {
  font-size: 28rpx;
  color: #1890ff;
}

/* 电影列表样式 */
.movie-scroll {
  white-space: nowrap;
}

.movie-list {
  display: inline-block;
  padding-left: 30rpx;
}

.movie-item {
  display: inline-block;
  width: 220rpx;
  margin-right: 20rpx;
  vertical-align: top;
}

.movie-poster {
  width: 220rpx;
  height: 300rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}

.movie-info {
  text-align: center;
}

.movie-name {
  display: block;
  font-size: 28rpx;
  color: #333;
  margin-bottom: 10rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.movie-rating {
  display: flex;
  justify-content: center;
  align-items: baseline;
}

.rating-score {
  font-size: 32rpx;
  color: #ff6b35;
  font-weight: bold;
}

.rating-text {
  font-size: 24rpx;
  color: #999;
  margin-left: 5rpx;
}

.movie-date {
  font-size: 24rpx;
  color: #999;
}
