import { createRouter, createWebHistory } from 'vue-router'
import Layout from '@/layout/index.vue'

const routes = [
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/Login.vue'),
    meta: { title: '登录' }
  },
  {
    path: '/',
    component: Layout,
    redirect: '/dashboard',
    children: [
      {
        path: 'dashboard',
        name: 'Dashboard',
        component: () => import('@/views/Dashboard.vue'),
        meta: { title: '首页', icon: 'House' }
      }
    ]
  },
  {
    path: '/movie',
    component: Layout,
    redirect: '/movie/list',
    meta: { title: '电影管理', icon: 'VideoPlay' },
    children: [
      {
        path: 'list',
        name: 'MovieList',
        component: () => import('@/views/movie/List.vue'),
        meta: { title: '电影列表', icon: 'List' }
      },
      {
        path: 'add',
        name: 'MovieAdd',
        component: () => import('@/views/movie/Add.vue'),
        meta: { title: '添加电影', icon: 'Plus' }
      }
    ]
  },
  {
    path: '/cinema',
    component: Layout,
    redirect: '/cinema/list',
    meta: { title: '影院管理', icon: 'OfficeBuilding' },
    children: [
      {
        path: 'list',
        name: 'CinemaList',
        component: () => import('@/views/cinema/List.vue'),
        meta: { title: '影院列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/schedule',
    component: Layout,
    redirect: '/schedule/list',
    meta: { title: '排片管理', icon: 'Calendar' },
    children: [
      {
        path: 'list',
        name: 'ScheduleList',
        component: () => import('@/views/schedule/List.vue'),
        meta: { title: '排片列表', icon: 'List' }
      }
    ]
  },
  {
    path: '/order',
    component: Layout,
    redirect: '/order/list',
    meta: { title: '订单管理', icon: 'Tickets' },
    children: [
      {
        path: 'list',
        name: 'OrderList',
        component: () => import('@/views/order/List.vue'),
        meta: { title: '订单列表', icon: 'List' }
      }
    ]
  }
]

const router = createRouter({
  history: createWebHistory(process.env.BASE_URL),
  routes
})

export default router
