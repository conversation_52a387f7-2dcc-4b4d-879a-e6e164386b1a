# 电影购票系统管理后台 (film_admin)

基于Vue 3 + Element Plus的电影购票系统管理后台。

## 技术栈

- **框架**: Vue 3.3.4
- **UI组件**: Element Plus 2.3.8
- **路由**: Vue Router 4.2.4
- **状态管理**: Vuex 4.1.0
- **HTTP客户端**: Axios 1.4.0
- **图表**: ECharts 5.4.3
- **构建工具**: Vue CLI 5.0.8

## 项目结构

```
film_admin/
├── public/
│   └── index.html                # HTML模板
├── src/
│   ├── assets/                   # 静态资源
│   │   ├── css/
│   │   └── images/
│   ├── components/               # 公共组件
│   ├── layout/                   # 布局组件
│   │   └── index.vue            # 主布局
│   ├── router/                   # 路由配置
│   │   └── index.js
│   ├── store/                    # Vuex状态管理
│   │   └── index.js
│   ├── utils/                    # 工具函数
│   │   ├── request.js           # HTTP请求封装
│   │   └── auth.js              # 认证相关
│   ├── views/                    # 页面组件
│   │   ├── Login.vue            # 登录页
│   │   ├── Dashboard.vue        # 首页
│   │   ├── movie/               # 电影管理
│   │   ├── cinema/              # 影院管理
│   │   ├── schedule/            # 排片管理
│   │   └── order/               # 订单管理
│   ├── App.vue                   # 根组件
│   └── main.js                   # 入口文件
├── package.json                  # 依赖配置
└── README.md
```

## 功能模块

### 1. 系统管理
- 管理员登录/退出
- 权限控制
- 系统设置

### 2. 电影管理
- 电影列表查看
- 电影信息添加/编辑/删除
- 电影状态管理（上架/下架）
- 电影海报上传

### 3. 影院管理
- 影院信息管理
- 影厅信息管理
- 座位布局设置

### 4. 排片管理
- 排片计划制定
- 场次时间安排
- 票价设置

### 5. 订单管理
- 订单列表查看
- 订单状态跟踪
- 退票处理

### 6. 数据统计
- 票房统计
- 用户统计
- 热门电影排行

## 快速开始

### 1. 环境要求

- Node.js 16+
- npm 8+ 或 yarn 1.22+

### 2. 安装依赖

```bash
# 使用npm
npm install

# 或使用yarn
yarn install
```

### 3. 启动开发服务器

```bash
# 使用npm
npm run serve

# 或使用yarn
yarn serve
```

启动成功后访问：http://localhost:8080

### 4. 构建生产版本

```bash
# 使用npm
npm run build

# 或使用yarn
yarn build
```

## 开发说明

### 1. 目录规范

- `views/` 目录按功能模块划分
- `components/` 存放可复用组件
- `utils/` 存放工具函数
- `assets/` 存放静态资源

### 2. 组件规范

- 使用Vue 3 Composition API
- 组件名使用PascalCase
- 文件名使用kebab-case

### 3. 样式规范

- 使用Sass预处理器
- 遵循BEM命名规范
- 响应式设计适配

### 4. API调用

```javascript
// 使用封装的request工具
import request from '@/utils/request'

// GET请求
const getMovieList = (params) => {
  return request({
    url: '/movie/page',
    method: 'get',
    params
  })
}

// POST请求
const addMovie = (data) => {
  return request({
    url: '/movie',
    method: 'post',
    data
  })
}
```

## 配置说明

### 1. 环境变量

创建 `.env.development` 和 `.env.production` 文件：

```bash
# .env.development
VUE_APP_BASE_API = 'http://localhost:8080/api'

# .env.production
VUE_APP_BASE_API = 'https://your-api-domain.com/api'
```

### 2. 代理配置

在 `vue.config.js` 中配置开发代理：

```javascript
module.exports = {
  devServer: {
    proxy: {
      '/api': {
        target: 'http://localhost:8080',
        changeOrigin: true,
        pathRewrite: {
          '^/api': '/api'
        }
      }
    }
  }
}
```

## 部署说明

### 1. Nginx配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    root /var/www/film-admin/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    location /api {
        proxy_pass http://localhost:8080;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
    }
}
```

### 2. Docker部署

```dockerfile
FROM nginx:alpine
COPY dist/ /usr/share/nginx/html/
COPY nginx.conf /etc/nginx/conf.d/default.conf
EXPOSE 80
CMD ["nginx", "-g", "daemon off;"]
```

## 默认账号

- 用户名：admin
- 密码：123456

## 注意事项

1. 首次运行需要确保后端API服务正常运行
2. 修改API地址配置以匹配实际后端服务
3. 生产环境建议开启HTTPS
4. 定期更新依赖包版本
